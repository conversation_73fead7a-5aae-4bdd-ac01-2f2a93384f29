<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1.0,user-scalable=0">
    <link rel="icon" href="<%= BASE_URL %>favicon.ico">
    <link rel="stylesheet" href="./element-ui/index.css">
    <title>格灵课堂-运营后台</title>
    <!--[if lt IE 9]>
    <style type="text/css" media="screen">
      html, body {
        width: 100%;
        height: 100%;
      }

      .bowser-mask {
        width: 1200px;
        margin: 0 auto;
        text-align: center;
      }

      .bowser-mask-header {
        font-size: 20px;
        color: #333333;
        margin-top: 180px;
        margin-bottom: 24px;
      }

      .bowser-mask-header span {
        color: #2ebe4f;
      }

      .bowser-mask-tip {
        font-size: 16px;
        color: #666666;
        margin-bottom: 80px;
      }

      .bowser-mask ul {
        overflow: hidden;
        white-space: nowrap;
        width: 540px;
        margin: 0 auto;
      }

      .bowser-mask li {
        width: 95px;
        float: left;
        list-style: none;
        margin-right: 48px;
      }

      .bowser-mask li a {
        width: 66px;
        height: 60px;
        display: inline-block;
        background-image: url('./images/bowser.png');
        background-repeat: no-repeat;
        margin-bottom: 8px;
      }

      .bowser-mask li div {
        font-size: 16px;
        color: #666666;
        margin-bottom: 3px;
      }

      .bowser-mask li span {
        font-size: 14px;
        color: #999999;
      }
    </style>
    <![endif]-->
  </head>
  <body>
    <!--[if lt IE 9]>
    <div class="bowser-mask">
      <div class="bowser-mask-header">使用一款 <span>优质浏览器，是学习课程</span> 的关键一步！</div>
      <div class="bowser-mask-tip">您当前使用的浏览器版本过低，将会影响到您的 浏览体验，建议使用更高版本浏览器</div>
      <ul>
        <li>
          <a href="http://www.firefox.com.cn" target="_blank"></a>
          <div>Firefox</div>
          <span>PC/Mac/Linux</span>
        </li>
        <li>
          <a href="https://www.google.cn/chrome/index.html" target="_blank" style="background-position: 0 -70px;"></a>
          <div>Chrome</div>
          <span>PC/Mac/Linux</span>
        </li>
        <li>
          <a href="https://support.microsoft.com/zh-cn/help/17621/internet-explorer-downloads" target="_blank"
             style="background-position: 0 -139px;"></a>
          <div>IE 11</div>
          <span>PC</span>
        </li>
        <li style="margin-right: 0;">
          <a href="https://support.apple.com/zh_CN/downloads/safari" target="_blank"
             style="background-position: 0 -223px;"></a>
          <div>safari</div>
          <span>Mac</span>
        </li>
      </ul>
    </div>
    <![endif]-->
    <noscript>
      <strong>We're sorry but vue_cli_3.x doesn't work properly without JavaScript enabled. Please enable it to
        continue.</strong>
    </noscript>
    <div id="app"></div>
    <!-- built files will be auto injected -->
    <script src="./vue/vue.min.js?v2.5.2"></script>
    <script src="./vue-router/vue-router.min.js?v3.0.1"></script>
    <script src="./vuex/vuex.min.js?v3.0.1"></script>
    <script src="./axios/axios.min.js?v0.19.0"></script>
    <script src="./element-ui/index.js?v2.8.2"></script>
  </body>
</html>
