{"name": "vue_cli_3.x", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "dev": "vue-cli-service serve", "dev:product": "vue-cli-service serve --mode production", "dev:test": "vue-cli-service serve --mode test", "start": "vue-cli-service serve", "build": "vue-cli-service build", "build:test": "vue-cli-service build --mode test"}, "dependencies": {"core-js": "^2.6.11", "echarts": "^4.9.0", "jsencrypt": "^3.0.0-rc.1", "jszip": "^3.2.2", "vant": "2.2.13", "vue-aplayer": "^1.6.1", "vue-resource": "^1.5.1", "vue-video-player": "^5.0.2", "vuex-persistedstate": "^2.7.0"}, "devDependencies": {"@vue/cli-plugin-babel": "^3.12.1", "@vue/cli-service": "^3.12.1", "axios": "^0.19.0", "less": "^3.12.2", "less-loader": "^4.1.0", "postcss-px2rem": "^0.3.0", "vue-template-compiler": "^2.6.12"}, "postcss": {"plugins": {"autoprefixer": {}}}, "browserslist": ["> 1%", "last 2 versions"]}