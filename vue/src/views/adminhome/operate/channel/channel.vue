<template>
  <div class="channel p20">
    <gl-condition
      class="mb10"
      :list="conditions"
      @button="btnEvent"
    ></gl-condition>
    <gl-table
      :data="tableData"
      row-key="id"
      :columns="columns"
      @sort-change="sortChange"
      :expands="expands"
      :pageBtnList="pageBtnList"
      @button="tableCallback"
    >
    </gl-table>
    <gl-pagination
      @callback="paginationChange"
      :total="total"
      :size="limits"
      :curr="curr"
    ></gl-pagination>
    <gl-dialog
      :title="text"
      v-model="dialogVisible"
      width="50%"
      @submit="submit"
    >
      <gl-form
        :form="form"
        :formLabel="formLabel1"
        must="name"
        ref="form"
        v-if="isMainChannel"
      ></gl-form>
      <gl-form
        :form="form"
        :formLabel="formLabel"
        must="name,pid"
        ref="form"
        v-else
      ></gl-form>
    </gl-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
export default {
  name: 'ChannelIndex',
  data() {
    return {
      conditions: [
        {placeholder: '输入名搜索', width: '320px', prop: 'keyword', value: ''},
        {type: 'button', text: '新建主渠道', right: true}
      ],
      tableData: [],
      columns: [
        {prop: 'id', label: 'ID', sortable: 'custom'},
        {prop: 'name', label: '上架渠道'},
        {prop: 'divide', label: '公司占比', },
        {prop: 'magnitude', label: '渠道量级', sortable: 'custom'},
        {prop: 'create_time', label: '上架日期', format: val => this.tools.formatDate(val, 'Y年MM月DD日')},
        // {label: '操作', btns: ({row})=> row.pid?['编辑', '查看产品']:['编辑', '添加子渠道']},
        {
          label: '操作',
          key: 'status',
          value: {
            1:'查看产品',
            2:'添加子渠道'
          },
          width: 180,
          extraBtn:['编辑'],
          btnsList: ((row, btnName, item, extraBtns, btnLink) => {
            this.btnLink = btnLink
            let btns = []
            let btnTest = btnName
            if (!btnName){
              for(let item of this.pageBtnList){
                if (item.name === '查看产品'){
                  btnTest = item.name
                  break
                }
              }
            }
            btns.push(btnTest)
            this.tableBtnList = [...extraBtns,...btns]
            return [...extraBtns,...btns]
          }),
        }
      ],
      form: {},
      formLabel: [
        {label: '渠道: ', prop: 'name',},
        {
          label: '主渠道: ',
          prop: 'pid',
          options: []
        },
        {
          label: '公司占比: ',
          prop: 'divide',
          options: []
        },
        {label: '渠道量级: ', prop: 'magnitude',},
        {label: '渠道费率: ', prop: 'commission_rate',},
        {
            label: '合作方: ',
            prop: 'cooperation_type',
            radio: [{label: '甲方', value: 1}, {label: '乙方', value: 2}],
            width: '150px'
          },
          {
            label: '结算周期: ',
            prop: 'settlement_cycle',
            radio: [{label: '月结', value: 1}, {label: '季结', value: 2}],
            width: '150px'
          },
          {label: '账户信息: ', prop: 'account_info', placeholder: '请输入账号信息', width: '150px'}, 
          {label: '开户银行: ', prop: 'bank_name', placeholder: '请输入开户银行', width: '150px'},
          {label: '开户账号: ', prop: 'bank_account', placeholder: '请输入开户账号',width: '150px'},
          {label: '联系地址: ', prop: 'address', placeholder: '请输入联系地址', width: '150px'},
          {label: '开票信息: ', prop: 'invoice_info', placeholder: '请输入开票信息',type: 'textarea'},
        {
          label: '资源年份限制: ',
          prop: 'nianfen',
          options: []
        },
      ],
      formLabel1: [
        {label: '渠道名: ', prop: 'name',},
      ],
      dialogVisible: false,
      total: 0,
      limits: 10,
      curr: 1,
      edit: false,
      keyword: '',
      order: '',
      isMainChannel:false,
      text:'',
      expands:[],

      btnLink:{},
      tableBtnList:[]
    }
  },
  computed: {
    ...mapGetters(['roleList']),
  },
  created() {
    // 防止页面跳转表格按钮制空
    this.pageBtnList = this.roleList.btn_list

    this.getTableList(1);
    this.initOptions();
  },
  methods: {
    initOptions() {
      let objs = {divide: null, pid: null};
      for (let item of this.formLabel) {
        (item.prop === 'divide' || item.prop === 'pid') && (objs[item.prop] = item);
        if (objs.divide && objs.pid) break;
      }
      for (let i = 100; i > 0; i -= 10) {
        objs.divide.options.push({label: i + '%', value: i});
      }
      this.getMainChannel();
    },
    getTableList(bool) {
      if (bool) {
        this.curr = 1;
      }
      let {curr, limits, order, conditions} = this;
      let params = {
        curr,
        limits,
        order
      };
      conditions.forEach(e => {
        e.prop && (params[e.prop] = e.value);
      });
      this.http.$post('admin/resource.api/channel_list', params).then(res => {
        res.data.map(val => {
          if(!val.pid){
           val.divide = '--';
          }
          val.status = 2
        });
        this.tableData = Object.values(res.data);
        this.expands = this.tableData.filter(e => e.children && e.children.length).map(e => e.id);
        this.total = res.total;
      }).catch(this.$err)
    },
    // 主渠道列表
    getMainChannel(){
      this.http.$post('admin/resource.api/channel_simple_list',{pid:1},{loading:false}).then(res => {
        this.formLabel[1].options = res.map(e => ({label: e.name, value: e.id}));
      }).catch(console.log);
    },
    // 条件按钮
    btnEvent(item) {
      if (item.type === 'button') {
        // 添加渠道
        this.isMainChannel = true;
        this.text = '新建主渠道';
        this.form = {
          name: '',
        };
        this.dialogVisible = true;
      } else {
        this.getTableList(1)
      }
    },
    // 排序
    sortChange(column, prop, order) {
      if (prop && order) {
        this.order = prop + ' ' + order.replace('ending', '');
      } else {
        this.order = '';
      }
      this.getTableList(1);
    },
    tableCallback({row, index}) {
      this[['edits', 'viewProducts'][index]](row,index);
    },
    // 查看产品
    async viewProducts(scope,index) {
      let { btnLink, tableBtnList } = this
      if(scope.pid && btnLink['查看产品']){
        await this.getPageRole(btnLink['查看产品'].link_id);
        this.$router.push({
          name: btnLink['查看产品'].auth_mca,
          params: {
            cid: scope.id,
          },
          query:{
            keepAlive: 1,
          }
        })
      }else{
        this.text = '添加子渠道';
        this.isMainChannel = false;
        this.dialogVisible = true;
        this.form = {
          name: '',
          pid: scope.id,
          divide: '',
          magnitude: '',
        };
      }
    },
    // 编辑
    edits(scope) {
      scope.magnitude = String(scope.magnitude)
      this.form = {...scope};
      this.edit = true;
      this.dialogVisible = true;
      if(scope.pid){
        this.isMainChannel = false;
        this.text = '编辑子渠道';
      }else{
        this.isMainChannel = true;
        this.text = '编辑渠道';
      }
    },
    // 分页1
    paginationChange({size, curr}) {
      this.curr = curr;
      this.limits = size;
      this.getTableList()
    },
    // 弹框确定
    submit() {
      if (this.$refs.form.validate()) {
        let {form} = this;
        let reg = /^\S+$/;
        if(!reg.test(form.name)){
          return this.$message.info('不能输入空格符号!');
        }
        let url = form.id ? 'admin/resource.api/channel_edit' : 'admin/resource.api/channel_add';
        this.http.$post(url, form,{loading:false}).then(res => {
          this.$message.success(form.id ? '编辑成功！' : '添加成功');
          this.getTableList();
          this.dialogVisible = false;
          this.getMainChannel();
        }).catch(this.$err);
      }
    }
  }
}
</script>

<style lang="less" scoped>
.channel {
  background: #fff;
  .view-products {
    display: inline-block;
    color: #3cabff;
    font-size: 18px;
  }
}
</style>